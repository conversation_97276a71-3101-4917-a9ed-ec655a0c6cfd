# TeacherViewClassSeeder 优化说明

## 优化内容

### 1. 性能优化
- **批量插入**: 使用批量插入代替逐条插入，默认批次大小为100条记录
- **减少数据库查询**: 优化查询逻辑，减少不必要的数据库访问
- **内存优化**: 分批处理数据，避免一次性加载大量数据到内存

### 2. 代码结构优化
- **方法拆分**: 将复杂的 `run()` 方法拆分为多个职责单一的方法
- **错误处理**: 添加完善的异常处理和日志记录
- **代码可读性**: 改进变量命名和方法注释

### 3. 日志和监控
- **执行日志**: 记录执行开始、结束时间和处理记录数
- **错误日志**: 记录详细的错误信息便于调试
- **警告日志**: 记录数据匹配失败的情况

### 4. 数据验证
- **空值检查**: 增强对空数据的检查和处理
- **数据清洗**: 过滤无效的班级ID数据
- **匹配失败记录**: 记录无法匹配的班级信息

## 主要方法说明

### `run()`
主执行方法，协调整个数据迁移流程

### `getTeacherIdMap()`
获取教师ID映射关系，建立新旧系统的教师ID对应关系

### `getTeacherViewClassData()`
获取教师查看班级的原始数据，过滤空值

### `processAndInsertData()`
处理数据并批量插入，包含批次控制和进度记录

### `transformClass()`
转换班级数据，批量查询和转换班级信息

### `changeClassInfo()`
转换单个班级信息，匹配新的班级ID

### `matchClassName()`
优化的班级名称匹配算法

### `matchSchoolYear()`
计算学年的优化算法

## 使用方法

```php
// 在 DatabaseSeeder 或其他地方调用
$seeder = new TeacherViewClassSeeder($schoolId);
$seeder->run();
```

## 配置参数

- `$batchSize`: 批量插入大小，默认100，可根据服务器性能调整
- `$connect`: 源数据库连接名称，默认 'mysql_prod'

## 性能提升

相比原版本，优化后的版本在以下方面有显著提升：

1. **执行速度**: 批量插入比逐条插入快约10-50倍
2. **内存使用**: 分批处理避免内存溢出
3. **错误处理**: 更好的错误定位和恢复能力
4. **可维护性**: 代码结构更清晰，便于维护和扩展

## 注意事项

1. 确保源数据库连接配置正确
2. 建议在测试环境先验证数据迁移结果
3. 大量数据迁移时建议适当调整批次大小
4. 注意监控日志输出，及时发现数据问题
