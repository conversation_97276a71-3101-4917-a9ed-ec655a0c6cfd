<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class TeacherViewClassSeeder extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';
    protected int $batchSize = 100; // 批量插入大小

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $startTime = microtime(true);
        Log::info("开始执行 TeacherViewClassSeeder，学校ID: {$this->school_id}");

        try {
            // 获取教师ID映射关系
            $teacherIdMap = $this->getTeacherIdMap();
            if (empty($teacherIdMap)) {
                Log::warning("未找到学校ID {$this->school_id} 的教师数据");
                return;
            }

            // 获取教师查看班级数据
            $viewClassData = $this->getTeacherViewClassData($teacherIdMap);
            if (empty($viewClassData)) {
                Log::warning("未找到教师查看班级数据");
                return;
            }

            // 处理并批量插入数据
            $this->processAndInsertData($viewClassData);

            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            Log::info("TeacherViewClassSeeder 执行完成，耗时: {$executionTime}秒");

        } catch (\Exception $e) {
            Log::error("TeacherViewClassSeeder 执行失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取教师ID映射关系
     */
    protected function getTeacherIdMap(): array
    {
        return DB::table('teachers')
            ->where('school_id', $this->school_id)
            ->pluck('teacher_id', 'id')
            ->toArray();
    }

    /**
     * 获取教师查看班级数据
     */
    protected function getTeacherViewClassData(array $teacherIdMap): array
    {
        return DB::connection($this->connect)
            ->table('teacher')
            ->whereIn('id', array_values($teacherIdMap))
            ->whereNotNull('view_class_ids')
            ->where('view_class_ids', '!=', '')
            ->select('member_id', 'view_class_ids')
            ->get()
            ->toArray();
    }
    /**
     * 处理并批量插入数据
     */
    protected function processAndInsertData(array $viewClassData): void
    {
        $insertData = [];
        $processedCount = 0;
        $now = Carbon::now();

        foreach ($viewClassData as $item) {
            $teacherId = $item->member_id;
            $viewClassIds = array_filter(explode(',', $item->view_class_ids));

            if (empty($viewClassIds)) {
                continue;
            }

            // 转化班级数据，找到新的班级id
            $transformedClasses = $this->transformClass($viewClassIds);

            foreach ($transformedClasses as $classInfo) {
                if (!empty($classInfo['class_id'])) {
                    $insertData[] = [
                        'teacher_id' => $teacherId,
                        'class_id' => $classInfo['class_id'],
                        'school_year' => $classInfo['school_year'],
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];

                    // 批量插入
                    if (count($insertData) >= $this->batchSize) {
                        $this->batchInsert($insertData);
                        $processedCount += count($insertData);
                        $insertData = [];
                        Log::info("已处理 {$processedCount} 条教师查看班级记录");
                    }
                }
            }
        }

        // 插入剩余数据
        if (!empty($insertData)) {
            $this->batchInsert($insertData);
            $processedCount += count($insertData);
        }

        Log::info("总共处理了 {$processedCount} 条教师查看班级记录");
    }

    /**
     * 批量插入数据
     */
    protected function batchInsert(array $data): void
    {
        if (empty($data)) {
            return;
        }

        try {
            DB::table('teacher_view_classes')->insert($data);
        } catch (\Exception $e) {
            Log::error("批量插入教师查看班级数据失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 转换班级数据（优化版本）
     */
    protected function transformClass(array $classIds): array
    {
        if (empty($classIds)) {
            return [];
        }

        // 一次性查询所有班级数据
        $classList = DB::connection($this->connect)
            ->table('class')
            ->join('grade', 'grade.id', '=', 'class.grade_id')
            ->whereIn('class.id', $classIds)
            ->where('class.school_id', $this->school_id)
            ->where('class.step', 0)
            ->where('grade.step', 0)
            ->selectRaw('class.id, class.name, class.school_district, grade.grade_sort as grade_id, grade.name as grade_name')
            ->get()
            ->toArray();

        // 批量转换班级信息
        $classData = [];
        foreach ($classList as $item) {
            $newClassInfo = $this->changeClassInfo($item);
            if (!empty($newClassInfo['class_id'])) {
                $classData[] = $newClassInfo;
            } else {
                Log::warning("无法找到对应的新班级ID", [
                    'old_class_id' => $item->id,
                    'class_name' => $item->name,
                    'grade_id' => $item->grade_id
                ]);
            }
        }

        return $classData;
    }
    /**
     * 转换旧的班级数据对应到新的班级ID（优化版本）
     */
    protected function changeClassInfo($classInfo): array
    {
        $name = $classInfo->name;
        $grade_id = intval($classInfo->grade_id);
        $grade_name = $classInfo->grade_name;

        // 新的班级名称
        $newClassName = $this->matchClassName($name);

        // 查询新的班级ID
        $classId = DB::table('classes')
            ->where('class_name', $newClassName)
            ->where('school_id', $this->school_id)
            ->where('school_campus_id', $classInfo->school_district)
            ->where('grade_id', $grade_id)
            ->value('id');

        // 计算学年
        $school_year = $this->matchSchoolYear($grade_id, $grade_name);

        return [
            'id' => $classInfo->id,
            'class_id' => $classId,
            'school_year' => $school_year,
        ];
    }

    /**
     * 匹配班级名称（优化版本）
     */
    protected function matchClassName(string $original_class_name): string
    {
        // 优先匹配括号里的数字
        if (preg_match('/\((\d+)\)/', $original_class_name, $matches)) {
            return "{$matches[1]}班";
        }

        // 匹配任意数字
        if (preg_match('/(\d+)/', $original_class_name, $matches)) {
            return "{$matches[1]}班";
        }

        // 如果都没匹配到，返回原名称
        return $original_class_name;
    }

    /**
     * 根据年级排序和年级名称推算学年（优化版本）
     * 1-5为小学 6-9为初中 10-12为高中
     */
    protected function matchSchoolYear(int $grade_sort, string $grade_year): int
    {
        // 将年级名称转换为数字（如果是数字字符串）
        $year = is_numeric($grade_year) ? intval($grade_year) : $this->extractYearFromGradeName($grade_year);

        // 根据年级阶段计算学年
        if ($grade_sort <= 5) {
            // 小学阶段：1-5年级
            return $year + $grade_sort - 1;
        } elseif ($grade_sort <= 9) {
            // 初中阶段：6-9年级
            return $year + $grade_sort - 6;
        } else {
            // 高中阶段：10-12年级
            return $year + $grade_sort - 10;
        }
    }

    /**
     * 从年级名称中提取年份
     */
    protected function extractYearFromGradeName(string $grade_name): int
    {
        // 尝试从年级名称中提取4位数年份
        if (preg_match('/(\d{4})/', $grade_name, $matches)) {
            return intval($matches[1]);
        }

        // 如果无法提取，返回当前年份作为默认值
        Log::warning("无法从年级名称中提取年份: {$grade_name}，使用当前年份");
        return intval(date('Y'));
    }
}

