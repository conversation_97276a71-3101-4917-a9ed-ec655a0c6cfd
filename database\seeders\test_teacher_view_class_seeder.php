<?php

/**
 * TeacherViewClassSeeder 测试脚本
 * 
 * 使用方法：
 * php artisan tinker
 * include 'database/seeders/test_teacher_view_class_seeder.php';
 */

use Database\Seeders\TeacherViewClassSeeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

function testTeacherViewClassSeeder($schoolId = 1) {
    echo "开始测试 TeacherViewClassSeeder...\n";
    
    try {
        // 记录开始时间
        $startTime = microtime(true);
        
        // 检查数据库连接
        echo "检查数据库连接...\n";
        $teacherCount = DB::table('teachers')->where('school_id', $schoolId)->count();
        echo "找到 {$teacherCount} 个教师记录\n";
        
        if ($teacherCount == 0) {
            echo "警告：未找到教师数据，请检查学校ID\n";
            return;
        }
        
        // 检查原始数据
        $originalCount = DB::table('teacher_view_classes')->count();
        echo "执行前 teacher_view_classes 表记录数: {$originalCount}\n";
        
        // 执行 seeder
        echo "执行 TeacherViewClassSeeder...\n";
        $seeder = new TeacherViewClassSeeder($schoolId);
        $seeder->run();
        
        // 检查结果
        $newCount = DB::table('teacher_view_classes')->count();
        $addedCount = $newCount - $originalCount;
        echo "执行后 teacher_view_classes 表记录数: {$newCount}\n";
        echo "新增记录数: {$addedCount}\n";
        
        // 计算执行时间
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);
        echo "总执行时间: {$executionTime} 秒\n";
        
        // 数据验证
        echo "\n数据验证:\n";
        $sampleData = DB::table('teacher_view_classes')
            ->join('teachers', 'teachers.teacher_id', '=', 'teacher_view_classes.teacher_id')
            ->join('classes', 'classes.id', '=', 'teacher_view_classes.class_id')
            ->where('teachers.school_id', $schoolId)
            ->select('teacher_view_classes.*', 'teachers.teacher_name', 'classes.class_name')
            ->limit(5)
            ->get();
            
        foreach ($sampleData as $record) {
            echo "教师: {$record->teacher_name}, 班级: {$record->class_name}, 学年: {$record->school_year}\n";
        }
        
        echo "\n测试完成！\n";
        
    } catch (Exception $e) {
        echo "测试失败: " . $e->getMessage() . "\n";
        echo "错误堆栈: " . $e->getTraceAsString() . "\n";
    }
}

function cleanupTestData($schoolId = 1) {
    echo "清理测试数据...\n";
    
    $deletedCount = DB::table('teacher_view_classes')
        ->whereIn('teacher_id', function($query) use ($schoolId) {
            $query->select('teacher_id')
                  ->from('teachers')
                  ->where('school_id', $schoolId);
        })
        ->delete();
        
    echo "删除了 {$deletedCount} 条记录\n";
}

// 使用示例：
// testTeacherViewClassSeeder(1);  // 测试学校ID为1的数据
// cleanupTestData(1);             // 清理测试数据
